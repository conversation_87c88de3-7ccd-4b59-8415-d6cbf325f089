import {
  Given,
  Then,
  Before,
  DataTable,
} from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  landingPage.loginLandingPage();
  cy.NavigateToTestFolder();
  cy.contains('FILES').click();
  cy.get('[data-testid^=files-table-row]')
    .should('be.visible')
    .and('have.length.gt', 0);
});

Given('The user is on the Files tab', () => {
  mediaListPage.getFilesTab().should('have.attr', 'aria-selected', 'true');
});

Then(
  'The following columns should be visible in the files table:',
  (dataTable: DataTable) => {
    const expectedHeaders = dataTable
      .raw()
      .slice(1)
      .map((row: string[]) => row[0]);
    mediaListPage.getTableHeaders().each(($header, index) => {
      const expectedHeader = expectedHeaders[index];
      if (expectedHeader) {
        cy.wrap($header)
          .find('span[role="button"]')
          .find('span')
          .first()
          .invoke('text')
          .should('eq', expectedHeader);
      } else {
        cy.wrap($header)
          .find('span[role="button"]')
          .find('span')
          .first()
          .invoke('text')
          .invoke('trim')
          .should('be.empty');
      }
    });
  }
);

Then('Each file row should have the correct data', () => {
  mediaListPage.verifyFileTableRows();
});

Then('The pagination control should be visible', () => {
  mediaListPage.getPagination().should('be.visible');
});
